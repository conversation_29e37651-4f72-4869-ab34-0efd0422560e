import { Injectable } from '@nestjs/common';
import { botsIdentifiers, botsList } from './botsData';
import { GameUserWithUserEntity } from 'src/game-users/models/entities/game-user.entity';
import { GameWithGameUsersWithUserEntity } from 'src/games/models/entities/game.entity';
import { AnswersModel } from 'src/questions/models/answers.model';

@Injectable()
export class BotsService {
  constructor() {}
  public selectBots(
    roomName: string,
    quizId: number,
    exclude: string[],
  ): GameUserWithUserEntity {
    const filteredItems = botsIdentifiers.filter(
      (item) => !exclude.includes(item),
    );

    if (filteredItems.length === 0) {
      return null;
    }

    const randomIndex = Math.floor(Math.random() * filteredItems.length);

    const res = <GameUserWithUserEntity>botsList[filteredItems[randomIndex]];
    res.roomName = roomName;
    res.quizId = quizId;
    res.userId = res.user.id;

    return res;
  }

  getBotsAnswers(gameData: GameWithGameUsersWithUserEntity): AnswersModel[] {
    const answers = [];
    const currentQuestion = gameData.data.questions[gameData.round - 1];

    gameData.game_users.forEach((gameUser) => {
      const isBot = botsIdentifiers.includes(gameUser.user.identifier);
      if (isBot) {
        const optionIndex = Math.floor(
          Math.random() * currentQuestion.questions_options.length,
        );
        const randomOption = currentQuestion.questions_options[optionIndex];

        answers.push(<AnswersModel>{
          currentRound: gameData.round,
          questionId: currentQuestion.id,
          userIndentifier: gameUser.user.identifier,
          answer: randomOption.value,
          user_id: Number(gameUser.user.id),
          is_correct_answer: randomOption.is_correct_answer == 1,
          questions_options_id: String(randomOption.id),
        });
      }
    });
    return answers;
  }
}
