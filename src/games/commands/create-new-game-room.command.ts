import GameModeEnumerator from '../models/enumerators/game-mode.enumerator';
import { QuestionsModel } from 'src/questions/models/questions.model';
import GameConfigParamsModel from 'src/shared/models/game-config-params.model';

export class CreateNewGameRoomCommand {
  constructor(
    public readonly gameMode: GameModeEnumerator,
    public readonly quizId: number,
    public readonly remoteConfigData: GameConfigParamsModel,
    public readonly questions: QuestionsModel[],
    public readonly roomName?: string,
  ) {}
}
