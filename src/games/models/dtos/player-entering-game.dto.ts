import GameModeEnumerator from '../enumerators/game-mode.enumerator';
import { UsersModel } from '../users.model';

export default interface PlayerEnteringGameDTO {
  gameMode: GameModeEnumerator;
  quizId: number;
  roomName: string;
  userData: UsersModel;
  isBot?: boolean;
}

export function isInstanceOfPlayerEnteringGameDTO(
  object: any,
): object is PlayerEnteringGameDTO {
  return object?.hasOwnProperty('gameMode');
}
