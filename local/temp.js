const fs = require('fs').promises;
const https = require('https'); // 1. Importar o módulo https
const { URL } = require('url');

// Para Node.js < v18, instale node-fetch: npm install node-fetch
// Para Node.js >= v18, fetch está disponível globalmente, mas precisamos de uma implementação customizada
// que suporte o agente HTTPS para certificados inválidos

// 2. Criar um agente HTTPS que ignora a verificação de certificado
const unsafeHttpsAgent = new https.Agent({
  rejectUnauthorized: false, // ATENÇÃO: ISSO DESABILITA A VERIFICAÇÃO DO CERTIFICADO SSL
});

// 3. Implementação customizada de fetch que suporta agente HTTPS
async function customFetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const isHttps = parsedUrl.protocol === 'https:';

    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      agent: isHttps ? unsafeHttpsAgent : undefined,
    };

    const request = (isHttps ? https : require('http')).request(requestOptions, (response) => {
      let data = '';

      response.on('data', (chunk) => {
        data += chunk;
      });

      response.on('end', () => {
        const result = {
          ok: response.statusCode >= 200 && response.statusCode < 300,
          status: response.statusCode,
          statusText: response.statusMessage,
          headers: response.headers,
          json: async () => JSON.parse(data),
          text: async () => data,
        };
        resolve(result);
      });
    });

    request.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      request.write(options.body);
    }

    request.end();
  });
}

async function processarMultiplosUsuarios(todosDadosEntrada) {
  const url = 'https://**********:2053/v1/register'; // URL com porta não padrão HTTPS
  let objetosDeSaidaAgregados = {};
  let listaDeIdentifiersAgregada = [];
  let relatorioErros = [];

  console.warn(`
  *******************************************************************************
  ATENÇÃO: A VERIFICAÇÃO DE CERTIFICADO SSL ESTÁ DESABILITADA PARA AS CHAMADAS FETCH.
  Esta configuração é INSEGURA e NÃO DEVE SER USADA EM PRODUÇÃO.
  Use apenas para desenvolvimento ou em redes internas estritamente controladas.
  *******************************************************************************
  `);

  for (const chaveUsuarioOriginal in todosDadosEntrada) {
    if (todosDadosEntrada.hasOwnProperty(chaveUsuarioOriginal)) {
      const dadosOriginaisDoUsuario = todosDadosEntrada[chaveUsuarioOriginal];
      const usuarioParaEnviar = dadosOriginaisDoUsuario.user;

      if (!usuarioParaEnviar) {
        console.warn(
          `Item com chave ${chaveUsuarioOriginal} não possui a propriedade 'user'. Pulando.`,
        );
        relatorioErros.push({
          chaveOriginal: chaveUsuarioOriginal,
          status: 'erro_local',
          mensagem: "Propriedade 'user' ausente no item de entrada.",
        });
        continue;
      }

      console.log(
        `Processando usuário com chave original: ${chaveUsuarioOriginal} (Nome: ${usuarioParaEnviar.name})`,
      );

      try {
        const response = await customFetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(usuarioParaEnviar),
        });

        if (response.ok) {
          const dadosRetornadosDoServidor = await response.json();
          console.log(
            `  Sucesso para ${chaveUsuarioOriginal}. Identifier retornado: ${dadosRetornadosDoServidor.identifier}`,
          );

          if (
            !dadosRetornadosDoServidor.identifier ||
            typeof dadosRetornadosDoServidor.id === 'undefined'
          ) {
            console.error(
              `  Resposta do servidor para ${chaveUsuarioOriginal} não contém 'identifier' ou 'id' esperado.`,
            );
            relatorioErros.push({
              chaveOriginal: chaveUsuarioOriginal,
              identifierEnviado: usuarioParaEnviar.identifier,
              status: 'erro_resposta_servidor',
              mensagem:
                'Resposta do servidor incompleta (sem identifier ou id).',
              dadosServidor: dadosRetornadosDoServidor,
            });
            continue;
          }

          objetosDeSaidaAgregados[dadosRetornadosDoServidor.identifier] = {
            userName: dadosOriginaisDoUsuario.userName,
            isConnected: dadosOriginaisDoUsuario.isConnected,
            isBot: dadosOriginaisDoUsuario.isBot,
            connectionId: dadosOriginaisDoUsuario.connectionId,
            id: dadosOriginaisDoUsuario.id,
            userId: dadosRetornadosDoServidor.id,
            quizId: dadosOriginaisDoUsuario.quizId,
            user: dadosRetornadosDoServidor,
          };

          listaDeIdentifiersAgregada.push(dadosRetornadosDoServidor.identifier);
        } else {
          const erroData = await response.text();
          console.error(
            `  Erro na API para ${chaveUsuarioOriginal} (Nome: ${usuarioParaEnviar.name}): ${response.status} - ${erroData}`,
          );
          relatorioErros.push({
            chaveOriginal: chaveUsuarioOriginal,
            identifierEnviado: usuarioParaEnviar.identifier,
            nameEnviado: usuarioParaEnviar.name,
            status: response.status,
            mensagem: `API Error: ${erroData}`,
          });
        }
      } catch (error) {
        console.error(
          `  Erro de fetch para ${chaveUsuarioOriginal} (Nome: ${usuarioParaEnviar.name}): ${error.message}`,
        );
        // Erros comuns aqui podem ser 'UNABLE_TO_VERIFY_LEAF_SIGNATURE' se o agente não for usado ou 'ECONNREFUSED' se o servidor estiver offline
        relatorioErros.push({
          chaveOriginal: chaveUsuarioOriginal,
          identifierEnviado: usuarioParaEnviar.identifier,
          nameEnviado: usuarioParaEnviar.name,
          status: 'erro_fetch',
          mensagem: error.message,
        });
      }
    }
  }

  // Salvar os objetos agregados como arquivos JSON (lógica inalterada)
  if (Object.keys(objetosDeSaidaAgregados).length > 0) {
    try {
      await fs.writeFile(
        'objetosDeSaidaAgregados.json',
        JSON.stringify(objetosDeSaidaAgregados, null, 2),
      );
      console.log('\nArquivo objetosDeSaidaAgregados.json salvo com sucesso!');
    } catch (fileError) {
      console.error(
        '\nErro ao salvar objetosDeSaidaAgregados.json:',
        fileError,
      );
      relatorioErros.push({
        tipo: 'salvamento_objetos_agregados',
        mensagem: fileError.message,
      });
    }
  } else {
    console.log(
      '\nNenhum usuário processado com sucesso para gerar objetosDeSaidaAgregados.json.',
    );
  }

  if (listaDeIdentifiersAgregada.length > 0) {
    try {
      await fs.writeFile(
        'listaDeIdentifiersAgregada.json',
        JSON.stringify(listaDeIdentifiersAgregada, null, 2),
      );
      console.log('Arquivo listaDeIdentifiersAgregada.json salvo com sucesso!');
    } catch (fileError) {
      console.error(
        'Erro ao salvar listaDeIdentifiersAgregada.json:',
        fileError,
      );
      relatorioErros.push({
        tipo: 'salvamento_lista_ids_agregada',
        mensagem: fileError.message,
      });
    }
  } else {
    console.log(
      'Nenhum identificador para salvar em listaDeIdentifiersAgregada.json.',
    );
  }

  const resumo = {
    totalItensEntrada: Object.keys(todosDadosEntrada).length,
    sucessos: listaDeIdentifiersAgregada.length,
    falhas: relatorioErros.length,
    errosDetalhados: relatorioErros,
  };
  console.log('\nResumo do Processamento:', resumo);
  return resumo;
}

// --- Exemplo de como usar a função (em um ambiente Node.js) ---
const dadosDeEntradaMultiplosExemplo = {
  '376116f1-8d0c-4878-8039-0ce56ae48d33': {
    userName: 'Ana_Aventura33',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '866d1059-7680-47a9-a8e0-eaf13bbf2c5a',
      apple_id: '',
      google_id: '',
      name: 'Ana_Aventura33',
      identifier: '376116f1-8d0c-4878-8039-0ce56ae48d33',
      created_at: '2024-10-08T19:22:28.000Z',
      updated_at: '2024-10-08T19:22:28.000Z',
      avatar: 'image_f423c4f9a68f265f2ec8.png',
      email: null,
      phone_number: null,
      id: '266519',
      avatarUrl: 'https://static.sportiz.app/image_f423c4f9a68f265f2ec8.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '1cf65467-bee1-475e-a108-673eedb93267': {
    userName: 'LukeMaverick',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a5d26e67-730b-400b-ae17-e3e490eadc71',
      apple_id: '',
      google_id: '',
      name: 'LukeMaverick',
      identifier: '1cf65467-bee1-475e-a108-673eedb93267',
      created_at: '2024-10-08T19:22:31.000Z',
      updated_at: '2024-10-08T19:22:31.000Z',
      avatar: 'image_e1fa11881920f92fe7e0.png',
      email: null,
      phone_number: null,
      id: '266520',
      avatarUrl: 'https://static.sportiz.app/image_e1fa11881920f92fe7e0.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'b575dcf0-c4b7-4735-8d15-6d6f133cd4f8': {
    userName: 'Kristina_Weber37',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '06ee42f3-e6ab-492e-a3bd-19e9df888389',
      apple_id: '',
      google_id: '',
      name: 'Kristina_Weber37',
      identifier: 'b575dcf0-c4b7-4735-8d15-6d6f133cd4f8',
      created_at: '2024-10-08T19:22:34.000Z',
      updated_at: '2024-10-08T19:22:34.000Z',
      avatar: 'image_95afc9f7497d514ddf0e.png',
      email: null,
      phone_number: null,
      id: '266521',
      avatarUrl: 'https://static.sportiz.app/image_95afc9f7497d514ddf0e.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5ba59687-a08b-41bf-a39d-f5665c9e654d': {
    userName: 'Olivia_Lobo27',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '0d369b32-2977-4b27-8f1a-0030bfdec75c',
      apple_id: '',
      google_id: '',
      name: 'Olivia_Lobo27',
      identifier: '5ba59687-a08b-41bf-a39d-f5665c9e654d',
      created_at: '2024-10-08T19:22:37.000Z',
      updated_at: '2024-10-08T19:22:37.000Z',
      avatar: 'image_2b36c9f22fde9233b29a.png',
      email: null,
      phone_number: null,
      id: '266522',
      avatarUrl: 'https://static.sportiz.app/image_2b36c9f22fde9233b29a.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '452a988a-521b-47f8-9811-51e273242de6': {
    userName: 'Victor_Star52',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '939760b9-933b-4c15-9f7c-1c105404a815',
      apple_id: '',
      google_id: '',
      name: 'Victor_Star52',
      identifier: '452a988a-521b-47f8-9811-51e273242de6',
      created_at: '2024-10-08T19:22:39.000Z',
      updated_at: '2024-10-08T19:22:39.000Z',
      avatar: 'image_c43e5aaacce0bcb5f216.png',
      email: null,
      phone_number: null,
      id: '266523',
      avatarUrl: 'https://static.sportiz.app/image_c43e5aaacce0bcb5f216.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '8b255507-0d72-4c9b-a413-50bf8213c7ba': {
    userName: 'Elisa_Maga26',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a8e13d13-2f62-428d-a7a3-8afd73ce90e4',
      apple_id: '',
      google_id: '',
      name: 'Elisa_Maga26',
      identifier: '8b255507-0d72-4c9b-a413-50bf8213c7ba',
      created_at: '2024-10-08T19:22:41.000Z',
      updated_at: '2024-10-08T19:22:41.000Z',
      avatar: 'image_d98bbdc773a448b44383.png',
      email: null,
      phone_number: null,
      id: '266524',
      avatarUrl: 'https://static.sportiz.app/image_d98bbdc773a448b44383.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'e0f9d540-2d0e-4511-b892-56920aa4b871': {
    userName: 'Jan_Wagner87',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '509ea056-a609-457f-9bec-e5414f5e355b',
      apple_id: '',
      google_id: '',
      name: 'Jan_Wagner87',
      identifier: 'e0f9d540-2d0e-4511-b892-56920aa4b871',
      created_at: '2024-10-08T19:22:45.000Z',
      updated_at: '2024-10-08T19:22:45.000Z',
      avatar: 'image_f8dc014be19d92a958b6.png',
      email: null,
      phone_number: null,
      id: '266526',
      avatarUrl: 'https://static.sportiz.app/image_f8dc014be19d92a958b6.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'f4305a18-2f0f-4b26-b0eb-97eda0029031': {
    userName: 'Marcelo_Drag\u00e3o41',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '15f2c050-4326-46b7-9bec-631f6dfc71b0',
      apple_id: '',
      google_id: '',
      name: 'Marcelo_Drag\u00e3o41',
      identifier: 'f4305a18-2f0f-4b26-b0eb-97eda0029031',
      created_at: '2024-10-08T19:22:48.000Z',
      updated_at: '2024-10-08T19:22:48.000Z',
      avatar: 'image_e93c4b8d6d8bb9a233fd.png',
      email: null,
      phone_number: null,
      id: '266527',
      avatarUrl: 'https://static.sportiz.app/image_e93c4b8d6d8bb9a233fd.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'f2c0f4c7-d092-4ea8-a496-394b99827879': {
    userName: 'chloe_26',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5522ac21-35c4-414e-8079-6ade5f83367d',
      apple_id: '',
      google_id: '',
      name: 'chloe_26',
      identifier: 'f2c0f4c7-d092-4ea8-a496-394b99827879',
      created_at: '2024-10-08T19:22:50.000Z',
      updated_at: '2024-10-08T19:22:50.000Z',
      avatar: 'image_f3d961c151bb0f249a2e.png',
      email: null,
      phone_number: null,
      id: '266528',
      avatarUrl: 'https://static.sportiz.app/image_f3d961c151bb0f249a2e.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '*************-4388-9b59-9682c635c98b': {
    userName: 'Helena_M\u00edstica59',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '42c206db-f15f-4226-b73b-055513f704fd',
      apple_id: '',
      google_id: '',
      name: 'Helena_M\u00edstica59',
      identifier: '*************-4388-9b59-9682c635c98b',
      created_at: '2024-10-08T19:22:53.000Z',
      updated_at: '2024-10-08T19:22:53.000Z',
      avatar: 'image_6c023383d88c218a3c15.png',
      email: null,
      phone_number: null,
      id: '266529',
      avatarUrl: 'https://static.sportiz.app/image_6c023383d88c218a3c15.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '383ab22b-097b-44a2-a8b2-aaccf1d2944b': {
    userName: 'Samuel_M\u00edstico89',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '2e8f98ab-06b1-45c0-a077-0a407028882a',
      apple_id: '',
      google_id: '',
      name: 'Samuel_M\u00edstico89',
      identifier: '383ab22b-097b-44a2-a8b2-aaccf1d2944b',
      created_at: '2024-10-08T19:22:56.000Z',
      updated_at: '2024-10-08T19:22:56.000Z',
      avatar: 'image_c1efa5b2d68f3a042687.png',
      email: null,
      phone_number: null,
      id: '266530',
      avatarUrl: 'https://static.sportiz.app/image_c1efa5b2d68f3a042687.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '15566250-ec2b-40b5-a51e-51ca632a0357': {
    userName: 'Carla_Fantasma91',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '52d0ed63-a2bc-4030-9ed6-f035db9638c7',
      apple_id: '',
      google_id: '',
      name: 'Carla_Fantasma91',
      identifier: '15566250-ec2b-40b5-a51e-51ca632a0357',
      created_at: '2024-10-08T19:22:59.000Z',
      updated_at: '2024-10-08T19:22:59.000Z',
      avatar: 'image_6b0b3b12b310145a7684.png',
      email: null,
      phone_number: null,
      id: '266531',
      avatarUrl: 'https://static.sportiz.app/image_6b0b3b12b310145a7684.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '9d46d111-2125-4d3d-b272-d96b8a928031': {
    userName: 'Beatriz_Alma16',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'b35fa878-1f80-4aec-87fe-1f0c3c6c3a16',
      apple_id: '',
      google_id: '',
      name: 'Beatriz_Alma16',
      identifier: '9d46d111-2125-4d3d-b272-d96b8a928031',
      created_at: '2024-10-08T19:23:02.000Z',
      updated_at: '2024-10-08T19:23:02.000Z',
      avatar: 'image_e27eaa3f884e5828cf0c.png',
      email: null,
      phone_number: null,
      id: '266532',
      avatarUrl: 'https://static.sportiz.app/image_e27eaa3f884e5828cf0c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0d26fafc-902c-4f23-9409-2db23b95554b': {
    userName: 'Max_Ace',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '0bcc6936-31e7-4cc7-a1f3-6ccc1971f5d4',
      apple_id: '',
      google_id: '',
      name: 'Max_Ace',
      identifier: '0d26fafc-902c-4f23-9409-2db23b95554b',
      created_at: '2024-10-08T19:23:04.000Z',
      updated_at: '2024-10-08T19:23:04.000Z',
      avatar: 'image_654fd37d70a47e450d63.png',
      email: null,
      phone_number: null,
      id: '266533',
      avatarUrl: 'https://static.sportiz.app/image_654fd37d70a47e450d63.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '1b133e10-a899-4c54-9c73-84c0f65875e5': {
    userName: 'Markus_Schwarz23',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'bde54333-b9f8-4c62-8455-2bdd73d00a7a',
      apple_id: '',
      google_id: '',
      name: 'Markus_Schwarz23',
      identifier: '1b133e10-a899-4c54-9c73-84c0f65875e5',
      created_at: '2024-10-08T19:23:07.000Z',
      updated_at: '2024-10-08T19:23:07.000Z',
      avatar: 'image_1dc7f0b6a2c5456b9659.png',
      email: null,
      phone_number: null,
      id: '266535',
      avatarUrl: 'https://static.sportiz.app/image_1dc7f0b6a2c5456b9659.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'd5a23369-3394-4aab-ae05-ec8ece8ce449': {
    userName: 'Caio_Besta95',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1061e79c-**************-bee0ca8273de',
      apple_id: '',
      google_id: '',
      name: 'Caio_Besta95',
      identifier: 'd5a23369-3394-4aab-ae05-ec8ece8ce449',
      created_at: '2024-10-08T19:23:10.000Z',
      updated_at: '2024-10-08T19:23:10.000Z',
      avatar: 'image_e8a35f5ac9f92c645665.png',
      email: null,
      phone_number: null,
      id: '266536',
      avatarUrl: 'https://static.sportiz.app/image_e8a35f5ac9f92c645665.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '89213952-a067-4546-9e7d-bcac93c80fbe': {
    userName: 'Matthias_Lang34',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '6e2c8674-7f38-46e9-a047-e8d08d778dc5',
      apple_id: '',
      google_id: '',
      name: 'Matthias_Lang34',
      identifier: '89213952-a067-4546-9e7d-bcac93c80fbe',
      created_at: '2024-10-08T19:23:12.000Z',
      updated_at: '2024-10-08T19:23:12.000Z',
      avatar: 'image_69c797bae6ca71c3c88d.png',
      email: null,
      phone_number: null,
      id: '266539',
      avatarUrl: 'https://static.sportiz.app/image_69c797bae6ca71c3c88d.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '39f57135-c71e-42c6-9054-ddc1fa26545f': {
    userName: 'Bella_Spirit16',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5b8526e2-baef-4462-ac4b-4fc9347e1515',
      apple_id: '',
      google_id: '',
      name: 'Bella_Spirit16',
      identifier: '39f57135-c71e-42c6-9054-ddc1fa26545f',
      created_at: '2024-10-08T19:23:16.000Z',
      updated_at: '2024-10-08T19:23:16.000Z',
      avatar: 'image_cff5140156811d752a36.png',
      email: null,
      phone_number: null,
      id: '266540',
      avatarUrl: 'https://static.sportiz.app/image_cff5140156811d752a36.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '453fcc65-7431-401e-ae17-1ac64babfe7f': {
    userName: 'Jo\u00e3o_Guerreiro23',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '276a350e-cbca-4e04-b672-e4a457c5848d',
      apple_id: '',
      google_id: '',
      name: 'Jo\u00e3o_Guerreiro23',
      identifier: '453fcc65-7431-401e-ae17-1ac64babfe7f',
      created_at: '2024-10-08T19:23:18.000Z',
      updated_at: '2024-10-08T19:23:18.000Z',
      avatar: 'image_8b4370ba6cfb60452a77.png',
      email: null,
      phone_number: null,
      id: '266541',
      avatarUrl: 'https://static.sportiz.app/image_8b4370ba6cfb60452a77.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0c2a60ec-7c05-46a2-b8f9-ce10d5ed6d4e': {
    userName: 'Fiona_Wonder85',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'ddebe26c-c7cf-46b8-be54-873544cbbb54',
      apple_id: '',
      google_id: '',
      name: 'Fiona_Wonder85',
      identifier: '0c2a60ec-7c05-46a2-b8f9-ce10d5ed6d4e',
      created_at: '2024-10-08T19:23:21.000Z',
      updated_at: '2024-10-08T19:23:21.000Z',
      avatar: 'image_2a687ef023c9e80f8f14.png',
      email: null,
      phone_number: null,
      id: '266542',
      avatarUrl: 'https://static.sportiz.app/image_2a687ef023c9e80f8f14.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '6ab0ae6b-918f-45ec-a621-e3ccb1901941': {
    userName: 'David_Thunder',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'c5fb55a7-c926-488f-a844-5a31856de956',
      apple_id: '',
      google_id: '',
      name: 'David_Thunder',
      identifier: '6ab0ae6b-918f-45ec-a621-e3ccb1901941',
      created_at: '2024-10-08T19:23:23.000Z',
      updated_at: '2024-10-08T19:23:23.000Z',
      avatar: 'image_0b02b055fce9a1176d84.png',
      email: null,
      phone_number: null,
      id: '266543',
      avatarUrl: 'https://static.sportiz.app/image_0b02b055fce9a1176d84.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5f38908a-1237-473a-981b-b17c03745e92': {
    userName: 'Nick12Wolfster',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4d6432cb-89d2-42c5-9f43-438a9e601dc9',
      apple_id: '',
      google_id: '',
      name: 'Nick12Wolfster',
      identifier: '5f38908a-1237-473a-981b-b17c03745e92',
      created_at: '2024-10-08T19:23:26.000Z',
      updated_at: '2024-10-08T19:23:26.000Z',
      avatar: 'image_788e12087cf01403174f.png',
      email: null,
      phone_number: null,
      id: '266544',
      avatarUrl: 'https://static.sportiz.app/image_788e12087cf01403174f.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'cf3b53bc-c9c6-4498-a6bc-0a6b0c3c9e58': {
    userName: 'Felipe_Quimera67',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a510476c-df3c-4ed1-ad0a-cc691c5eaa7f',
      apple_id: '',
      google_id: '',
      name: 'Felipe_Quimera67',
      identifier: 'cf3b53bc-c9c6-4498-a6bc-0a6b0c3c9e58',
      created_at: '2024-10-08T19:23:29.000Z',
      updated_at: '2024-10-08T19:23:29.000Z',
      avatar: 'image_3eaa8ab17e6ad8065415.png',
      email: null,
      phone_number: null,
      id: '266545',
      avatarUrl: 'https://static.sportiz.app/image_3eaa8ab17e6ad8065415.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '78281c28-c9f3-4c97-94ca-c16e4e535c82': {
    userName: 'Love_explorer',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '52fd423b-64d2-4167-b111-9fdbab6a6665',
      apple_id: '',
      google_id: '',
      name: 'Love_explorer',
      identifier: '78281c28-c9f3-4c97-94ca-c16e4e535c82',
      created_at: '2024-10-08T19:23:32.000Z',
      updated_at: '2024-10-08T19:23:32.000Z',
      avatar: 'image_6bae15ff41e8fdc9642b.png',
      email: null,
      phone_number: null,
      id: '266547',
      avatarUrl: 'https://static.sportiz.app/image_6bae15ff41e8fdc9642b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'b98151fa-c813-4156-a06a-cfc0b4a36852': {
    userName: 'Anna_Becker91',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '350ac80f-016d-4232-9bef-50c2af175398',
      apple_id: '',
      google_id: '',
      name: 'Anna_Becker91',
      identifier: 'b98151fa-c813-4156-a06a-cfc0b4a36852',
      created_at: '2024-10-08T19:23:36.000Z',
      updated_at: '2024-10-08T19:23:36.000Z',
      avatar: 'image_96b04498bfd81abe5b70.png',
      email: null,
      phone_number: null,
      id: '266548',
      avatarUrl: 'https://static.sportiz.app/image_96b04498bfd81abe5b70.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'd0213373-9556-4702-bc22-40e9ba991bd4': {
    userName: 'Kevin_Hawk',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'c9875d07-01d5-4c48-bda7-fa6f6b2b253a',
      apple_id: '',
      google_id: '',
      name: 'Kevin_Hawk',
      identifier: 'd0213373-9556-4702-bc22-40e9ba991bd4',
      created_at: '2024-10-08T19:23:39.000Z',
      updated_at: '2024-10-08T19:23:39.000Z',
      avatar: 'image_ce952036e62aa6c324b6.png',
      email: null,
      phone_number: null,
      id: '266549',
      avatarUrl: 'https://static.sportiz.app/image_ce952036e62aa6c324b6.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '9789be88-c1cf-496d-88fb-c7c8fd0e10b3': {
    userName: 'Felix_Graf23',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'bd40e499-4cb5-4df5-bbd2-4fb17324bfc2',
      apple_id: '',
      google_id: '',
      name: 'Felix_Graf23',
      identifier: '9789be88-c1cf-496d-88fb-c7c8fd0e10b3',
      created_at: '2024-10-08T19:23:42.000Z',
      updated_at: '2024-10-08T19:23:42.000Z',
      avatar: 'image_700f6eebe02396be367f.png',
      email: null,
      phone_number: null,
      id: '266550',
      avatarUrl: 'https://static.sportiz.app/image_700f6eebe02396be367f.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '6a8f857b-a060-42c8-b755-e2d2b7e1042d': {
    userName: 'Marie_Schneider26',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '690f7532-9c86-42b5-aa02-754ee70e19f7',
      apple_id: '',
      google_id: '',
      name: 'Marie_Schneider26',
      identifier: '6a8f857b-a060-42c8-b755-e2d2b7e1042d',
      created_at: '2024-10-08T19:23:45.000Z',
      updated_at: '2024-10-08T19:23:45.000Z',
      avatar: 'image_70f734854d856187c178.png',
      email: null,
      phone_number: null,
      id: '266551',
      avatarUrl: 'https://static.sportiz.app/image_70f734854d856187c178.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '*************-4530-8acb-ead4e63a6229': {
    userName: 'george_love',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '965288e1-2551-44dc-9a2b-2b8846457d6f',
      apple_id: '',
      google_id: '',
      name: 'george_love',
      identifier: '*************-4530-8acb-ead4e63a6229',
      created_at: '2024-10-08T19:23:48.000Z',
      updated_at: '2024-10-08T19:23:48.000Z',
      avatar: 'image_a670db77c555d5cbcdea.png',
      email: null,
      phone_number: null,
      id: '266552',
      avatarUrl: 'https://static.sportiz.app/image_a670db77c555d5cbcdea.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '9759a47e-37b9-4993-8074-9bf02db09f67': {
    userName: 'Timo_Keller89',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'fcd4b178-ab1d-4ca3-b17f-a147650a7280',
      apple_id: '',
      google_id: '',
      name: 'Timo_Keller89',
      identifier: '9759a47e-37b9-4993-8074-9bf02db09f67',
      created_at: '2024-10-08T19:23:50.000Z',
      updated_at: '2024-10-08T19:23:50.000Z',
      avatar: 'image_94424575056dfe393b73.png',
      email: null,
      phone_number: null,
      id: '266553',
      avatarUrl: 'https://static.sportiz.app/image_94424575056dfe393b73.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '4ba48a16-3e42-47d0-ab69-e10581af3bc0': {
    userName: 'Julia_Klein33',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'ed2155b9-0d12-447c-9e82-fe5731a86904',
      apple_id: '',
      google_id: '',
      name: 'Julia_Klein33',
      identifier: '4ba48a16-3e42-47d0-ab69-e10581af3bc0',
      created_at: '2024-10-08T19:23:53.000Z',
      updated_at: '2024-10-08T19:23:53.000Z',
      avatar: 'image_52d90d71bb5cb53adef8.png',
      email: null,
      phone_number: null,
      id: '266554',
      avatarUrl: 'https://static.sportiz.app/image_52d90d71bb5cb53adef8.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5d5d298d-d777-45d4-8ac4-519136cf58bb': {
    userName: 'Lisa_Hofmann42',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '3ba4d880-0266-4415-9cde-95aba946bd70',
      apple_id: '',
      google_id: '',
      name: 'Lisa_Hofmann42',
      identifier: '5d5d298d-d777-45d4-8ac4-519136cf58bb',
      created_at: '2024-10-08T19:23:55.000Z',
      updated_at: '2024-10-08T19:23:55.000Z',
      avatar: 'image_d2bccf680532bba9f045.png',
      email: null,
      phone_number: null,
      id: '266555',
      avatarUrl: 'https://static.sportiz.app/image_d2bccf680532bba9f045.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '7954364d-6959-4e0d-8296-19b9c2daf6b0': {
    userName: 'Julia_Night18',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4d2ba38e-4f39-4500-b5cb-bdf3ccf15469',
      apple_id: '',
      google_id: '',
      name: 'Julia_Night18',
      identifier: '7954364d-6959-4e0d-8296-19b9c2daf6b0',
      created_at: '2024-10-08T19:23:58.000Z',
      updated_at: '2024-10-08T19:23:58.000Z',
      avatar: 'image_37d121dd9ecd287bd0d9.png',
      email: null,
      phone_number: null,
      id: '266556',
      avatarUrl: 'https://static.sportiz.app/image_37d121dd9ecd287bd0d9.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '1f6c2581-95c1-4007-ba2c-6e39283b46fd': {
    userName: 'Dana_Star64',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '50396977-c4c0-4f8b-8125-d521cc1691af',
      apple_id: '',
      google_id: '',
      name: 'Dana_Star64',
      identifier: '1f6c2581-95c1-4007-ba2c-6e39283b46fd',
      created_at: '2024-10-08T19:24:00.000Z',
      updated_at: '2024-10-08T19:24:00.000Z',
      avatar: 'image_0d1081415b1c395ae9eb.png',
      email: null,
      phone_number: null,
      id: '266557',
      avatarUrl: 'https://static.sportiz.app/image_0d1081415b1c395ae9eb.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '192d457b-db70-42a9-83fb-5857b28ad9d2': {
    userName: 'Logan_Beast98',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '197661af-2cc2-47ec-a94d-bbc5b23b755f',
      apple_id: '',
      google_id: '',
      name: 'Logan_Beast98',
      identifier: '192d457b-db70-42a9-83fb-5857b28ad9d2',
      created_at: '2024-10-08T19:24:03.000Z',
      updated_at: '2024-10-08T19:24:03.000Z',
      avatar: 'image_64ca5d5c3feec8d68146.png',
      email: null,
      phone_number: null,
      id: '266558',
      avatarUrl: 'https://static.sportiz.app/image_64ca5d5c3feec8d68146.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'c189e047-0a59-445a-adeb-cdb817406b71': {
    userName: 'Miguel_Fenix45',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1d11dbe3-5708-4d5a-9934-1a2135636202',
      apple_id: '',
      google_id: '',
      name: 'Miguel_Fenix45',
      identifier: 'c189e047-0a59-445a-adeb-cdb817406b71',
      created_at: '2024-10-08T19:24:04.000Z',
      updated_at: '2024-10-08T19:24:04.000Z',
      avatar: 'image_37ad40b5a51f9bd15c31.png',
      email: null,
      phone_number: null,
      id: '266559',
      avatarUrl: 'https://static.sportiz.app/image_37ad40b5a51f9bd15c31.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '2bd1c8e7-67e6-4502-8212-3873dfc7a2e0': {
    userName: 'John_Sage72',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a4eb46a1-b2ce-45fe-8151-154fd6234b5d',
      apple_id: '',
      google_id: '',
      name: 'John_Sage72',
      identifier: '2bd1c8e7-67e6-4502-8212-3873dfc7a2e0',
      created_at: '2024-10-08T19:24:08.000Z',
      updated_at: '2024-10-08T19:24:08.000Z',
      avatar: 'image_a05c3d38fbf8a393ed1b.png',
      email: null,
      phone_number: null,
      id: '266560',
      avatarUrl: 'https://static.sportiz.app/image_a05c3d38fbf8a393ed1b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '62888fc8-df2a-4655-a353-3d23fa94a6ea': {
    userName: 'Pedro_Mestre87',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '0a7cda27-0ad1-4ac7-8764-60aee6fbef49',
      apple_id: '',
      google_id: '',
      name: 'Pedro_Mestre87',
      identifier: '62888fc8-df2a-4655-a353-3d23fa94a6ea',
      created_at: '2024-10-08T19:24:10.000Z',
      updated_at: '2024-10-08T19:24:10.000Z',
      avatar: 'image_dadd6c44b8048b0fd7f2.png',
      email: null,
      phone_number: null,
      id: '266561',
      avatarUrl: 'https://static.sportiz.app/image_dadd6c44b8048b0fd7f2.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'b84417f9-1000-4ebc-a63f-5acaaf818781': {
    userName: 'alex_lover',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'cdb90370-0f40-4345-902c-53042882b22b',
      apple_id: '',
      google_id: '',
      name: 'alex_lover',
      identifier: 'b84417f9-1000-4ebc-a63f-5acaaf818781',
      created_at: '2024-10-08T19:24:13.000Z',
      updated_at: '2024-10-08T19:24:13.000Z',
      avatar: 'image_ee88dab1f971c1010256.png',
      email: null,
      phone_number: null,
      id: '266562',
      avatarUrl: 'https://static.sportiz.app/image_ee88dab1f971c1010256.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '4b5ac325-0fc9-451b-8d85-d722eee0f2f3': {
    userName: 'Monika_Herrmann49',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '2ee08639-b4e3-4cef-afb1-0ec8b9e55b5d',
      apple_id: '',
      google_id: '',
      name: 'Monika_Herrmann49',
      identifier: '4b5ac325-0fc9-451b-8d85-d722eee0f2f3',
      created_at: '2024-10-08T19:24:15.000Z',
      updated_at: '2024-10-08T19:24:15.000Z',
      avatar: 'image_554071d0572cc9ef8c2b.png',
      email: null,
      phone_number: null,
      id: '266563',
      avatarUrl: 'https://static.sportiz.app/image_554071d0572cc9ef8c2b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '243a2e1c-fc66-4102-b097-df954a8370ba': {
    userName: 'Fernanda_Maravilha85',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a4a69394-4e71-4ca9-8652-04cb1b6f8a65',
      apple_id: '',
      google_id: '',
      name: 'Fernanda_Maravilha85',
      identifier: '243a2e1c-fc66-4102-b097-df954a8370ba',
      created_at: '2024-10-08T19:24:18.000Z',
      updated_at: '2024-10-08T19:24:18.000Z',
      avatar: 'image_e9198cf19934404c8b1a.png',
      email: null,
      phone_number: null,
      id: '266565',
      avatarUrl: 'https://static.sportiz.app/image_e9198cf19934404c8b1a.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '4d1f3973-cb0d-4c26-aa70-780a837b7031': {
    userName: 'Nat\u00e1lia_Tempestade93',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '90bf09b7-2a54-457c-975a-e9f1506cf398',
      apple_id: '',
      google_id: '',
      name: 'Nat\u00e1lia_Tempestade93',
      identifier: '4d1f3973-cb0d-4c26-aa70-780a837b7031',
      created_at: '2024-10-08T19:24:21.000Z',
      updated_at: '2024-10-08T19:24:21.000Z',
      avatar: 'image_187fb2e3c854a6eb50c1.png',
      email: null,
      phone_number: null,
      id: '266566',
      avatarUrl: 'https://static.sportiz.app/image_187fb2e3c854a6eb50c1.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'a85667ab-32e6-4c2e-bd8f-1028b2b7de4a': {
    userName: 'Cara_Wraith91',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '9b221971-2850-4624-9c59-9a35300c776b',
      apple_id: '',
      google_id: '',
      name: 'Cara_Wraith91',
      identifier: 'a85667ab-32e6-4c2e-bd8f-1028b2b7de4a',
      created_at: '2024-10-08T19:24:22.000Z',
      updated_at: '2024-10-08T19:24:22.000Z',
      avatar: 'image_bb890cae5a6273f603b8.png',
      email: null,
      phone_number: null,
      id: '266567',
      avatarUrl: 'https://static.sportiz.app/image_bb890cae5a6273f603b8.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'd711f0d9-76be-46d3-b7dc-1fbcc5daf776': {
    userName: 'Alex_Wizard',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a0af0d74-8f2e-4f18-b7fb-3b7a89e3b9e9',
      apple_id: '',
      google_id: '',
      name: 'Alex_Wizard',
      identifier: 'd711f0d9-76be-46d3-b7dc-1fbcc5daf776',
      created_at: '2024-10-08T19:24:25.000Z',
      updated_at: '2024-10-08T19:24:25.000Z',
      avatar: 'image_73389512b439fa844b5c.png',
      email: null,
      phone_number: null,
      id: '266568',
      avatarUrl: 'https://static.sportiz.app/image_73389512b439fa844b5c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '8cb87ad7-39f5-4209-b119-c95bb071103b': {
    userName: 'Rafael_Hunter12',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'b9f18ee6-129f-4421-8bce-fbaed7b427ed',
      apple_id: '',
      google_id: '',
      name: 'Rafael_Hunter12',
      identifier: '8cb87ad7-39f5-4209-b119-c95bb071103b',
      created_at: '2024-10-08T19:24:25.000Z',
      updated_at: '2024-10-08T19:24:25.000Z',
      avatar: 'image_06e4e0909a5c3b6ef701.png',
      email: null,
      phone_number: null,
      id: '266569',
      avatarUrl: 'https://static.sportiz.app/image_06e4e0909a5c3b6ef701.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'ee7fc147-e46c-4ae0-aadb-5f5aa2fe6ef6': {
    userName: 'Iris_Spirit81',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '54d45c73-662d-473d-bfe9-f3f295db4fd5',
      apple_id: '',
      google_id: '',
      name: 'Iris_Spirit81',
      identifier: 'ee7fc147-e46c-4ae0-aadb-5f5aa2fe6ef6',
      created_at: '2024-10-08T19:24:28.000Z',
      updated_at: '2024-10-08T19:24:28.000Z',
      avatar: 'image_690b345ca10a247de9c2.png',
      email: null,
      phone_number: null,
      id: '266570',
      avatarUrl: 'https://static.sportiz.app/image_690b345ca10a247de9c2.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'bbb213f7-19d3-498c-ae56-082c6eb9c347': {
    userName: 'Lucas_Dragon76',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'e9e79c76-855e-4085-bb0b-a729fa02554c',
      apple_id: '',
      google_id: '',
      name: 'Lucas_Dragon76',
      identifier: 'bbb213f7-19d3-498c-ae56-082c6eb9c347',
      created_at: '2024-10-08T19:24:29.000Z',
      updated_at: '2024-10-08T19:24:29.000Z',
      avatar: 'image_26a41059b3e6b469be23.png',
      email: null,
      phone_number: null,
      id: '266571',
      avatarUrl: 'https://static.sportiz.app/image_26a41059b3e6b469be23.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '1e977b65-9cea-4d52-9627-b65868eec606': {
    userName: 'JackStrikes',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4bbc551c-99a2-4568-8f3c-448557d71bbb',
      apple_id: '',
      google_id: '',
      name: 'JackStrikes',
      identifier: '1e977b65-9cea-4d52-9627-b65868eec606',
      created_at: '2024-10-08T19:24:30.000Z',
      updated_at: '2024-10-08T19:24:30.000Z',
      avatar: 'image_ee6fddd74b33420935ef.png',
      email: null,
      phone_number: null,
      id: '266572',
      avatarUrl: 'https://static.sportiz.app/image_ee6fddd74b33420935ef.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '09b2e663-3f0a-408c-9815-779fcad3649c': {
    userName: 'Nicole_Berger56',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '51f0b14b-dd02-47e9-98d2-71c4b53e9f30',
      apple_id: '',
      google_id: '',
      name: 'Nicole_Berger56',
      identifier: '09b2e663-3f0a-408c-9815-779fcad3649c',
      created_at: '2024-10-08T19:24:33.000Z',
      updated_at: '2024-10-08T19:24:33.000Z',
      avatar: 'image_0d96b720097d7c10deb7.png',
      email: null,
      phone_number: null,
      id: '266573',
      avatarUrl: 'https://static.sportiz.app/image_0d96b720097d7c10deb7.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '20b11d0c-fc03-46a2-a938-e95df0ac3c90': {
    userName: 'Sandra_b',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '2392ba7e-3804-42f8-9604-e015c7cc43ed',
      apple_id: '',
      google_id: '',
      name: 'Sandra_b',
      identifier: '20b11d0c-fc03-46a2-a938-e95df0ac3c90',
      created_at: '2024-10-08T19:24:36.000Z',
      updated_at: '2024-10-08T19:24:36.000Z',
      avatar: 'image_5901e41a92aa633b894d.png',
      email: null,
      phone_number: null,
      id: '266574',
      avatarUrl: 'https://static.sportiz.app/image_5901e41a92aa633b894d.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5a06f82c-30a8-461e-9f15-a35b1e53da87': {
    userName: 'Sam_Fire28',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a1271f09-ff83-4980-8d58-3a551efb88f5',
      apple_id: '',
      google_id: '',
      name: 'Sam_Fire28',
      identifier: '5a06f82c-30a8-461e-9f15-a35b1e53da87',
      created_at: '2024-10-08T19:24:39.000Z',
      updated_at: '2024-10-08T19:24:39.000Z',
      avatar: 'image_19d77a746ae53628b001.png',
      email: null,
      phone_number: null,
      id: '266575',
      avatarUrl: 'https://static.sportiz.app/image_19d77a746ae53628b001.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '2a2b8956-de3d-40a4-88e9-228dd19111a1': {
    userName: 'Julian_Krause92',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '7e5d687f-5f5e-4b03-91a7-cb6a30ce8365',
      apple_id: '',
      google_id: '',
      name: 'Julian_Krause92',
      identifier: '2a2b8956-de3d-40a4-88e9-228dd19111a1',
      created_at: '2024-10-08T19:24:42.000Z',
      updated_at: '2024-10-08T19:24:42.000Z',
      avatar: 'image_cba10ef40c8628254cc7.png',
      email: null,
      phone_number: null,
      id: '266576',
      avatarUrl: 'https://static.sportiz.app/image_cba10ef40c8628254cc7.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'badce8c2-97cd-494d-9563-2d1569891b85': {
    userName: 'Laura_M\u00fcller16',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '7b14a3d7-73fd-4468-9ed6-6d92d2a9042b',
      apple_id: '',
      google_id: '',
      name: 'Laura_M\u00fcller16',
      identifier: 'badce8c2-97cd-494d-9563-2d1569891b85',
      created_at: '2024-10-08T19:24:44.000Z',
      updated_at: '2024-10-08T19:24:44.000Z',
      avatar: 'image_d9774ddf99b15af23b33.png',
      email: null,
      phone_number: null,
      id: '266577',
      avatarUrl: 'https://static.sportiz.app/image_d9774ddf99b15af23b33.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'e62aa2ba-9d59-408e-b18c-70189de4773c': {
    userName: 'MichaelStorm',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '64ce7795-ca7e-4ad1-bdd4-889b9d5f1bbf',
      apple_id: '',
      google_id: '',
      name: 'MichaelStorm',
      identifier: 'e62aa2ba-9d59-408e-b18c-70189de4773c',
      created_at: '2024-10-08T19:24:47.000Z',
      updated_at: '2024-10-08T19:24:47.000Z',
      avatar: 'image_30918a1fe4b5f04368e8.png',
      email: null,
      phone_number: null,
      id: '266578',
      avatarUrl: 'https://static.sportiz.app/image_30918a1fe4b5f04368e8.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'fbbf5837-f996-429d-96d1-f94bac619ab1': {
    userName: 'Melanie_Lehmann85',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '9c68887b-47f1-4296-8372-38b0711328e4',
      apple_id: '',
      google_id: '',
      name: 'Melanie_Lehmann85',
      identifier: 'fbbf5837-f996-429d-96d1-f94bac619ab1',
      created_at: '2024-10-08T19:24:49.000Z',
      updated_at: '2024-10-08T19:24:49.000Z',
      avatar: 'image_903190ac6265cb843aa9.png',
      email: null,
      phone_number: null,
      id: '266579',
      avatarUrl: 'https://static.sportiz.app/image_903190ac6265cb843aa9.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'bd323261-3985-46e5-8174-b87bf3e63617': {
    userName: 'Ethan_Eagle',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '02c43636-98b1-4162-b7d2-6813e8bf1fd7',
      apple_id: '',
      google_id: '',
      name: 'Ethan_Eagle',
      identifier: 'bd323261-3985-46e5-8174-b87bf3e63617',
      created_at: '2024-10-08T19:24:52.000Z',
      updated_at: '2024-10-08T19:24:52.000Z',
      avatar: 'image_ba3becd5aaf7c609a373.png',
      email: null,
      phone_number: null,
      id: '266580',
      avatarUrl: 'https://static.sportiz.app/image_ba3becd5aaf7c609a373.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'cfc0379d-7ae3-4cd6-bb9f-bb08ea7c2351': {
    userName: 'LiamWarrior',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1f396efd-151e-4a64-83b0-33c02cadd39f',
      apple_id: '',
      google_id: '',
      name: 'LiamWarrior',
      identifier: 'cfc0379d-7ae3-4cd6-bb9f-bb08ea7c2351',
      created_at: '2024-10-08T19:24:54.000Z',
      updated_at: '2024-10-08T19:24:54.000Z',
      avatar: 'image_a34c5b6f81bc6c911109.png',
      email: null,
      phone_number: null,
      id: '266581',
      avatarUrl: 'https://static.sportiz.app/image_a34c5b6f81bc6c911109.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5b645123-7528-4803-99dc-c4b58b75a22f': {
    userName: 'Hanna_Mystic59',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5a8ccae8-b4e5-4879-96ce-15305e7c0507',
      apple_id: '',
      google_id: '',
      name: 'Hanna_Mystic59',
      identifier: '5b645123-7528-4803-99dc-c4b58b75a22f',
      created_at: '2024-10-08T19:24:56.000Z',
      updated_at: '2024-10-08T19:24:56.000Z',
      avatar: 'image_090db53195d309544b5b.png',
      email: null,
      phone_number: null,
      id: '266582',
      avatarUrl: 'https://static.sportiz.app/image_090db53195d309544b5b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '45aebdcd-bc75-4c68-b824-f6c418eb502c': {
    userName: 'Victor_X',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '16ba9f64-c251-44b2-bef7-917bb10cc5d0',
      apple_id: '',
      google_id: '',
      name: 'Victor_X',
      identifier: '45aebdcd-bc75-4c68-b824-f6c418eb502c',
      created_at: '2024-10-08T19:24:58.000Z',
      updated_at: '2024-10-08T19:24:58.000Z',
      avatar: 'image_8fe4d450499ba1d928db.png',
      email: null,
      phone_number: null,
      id: '266583',
      avatarUrl: 'https://static.sportiz.app/image_8fe4d450499ba1d928db.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '50cf2621-7784-428a-a263-14bfe7565099': {
    userName: 'Tobias_Brandt12',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '7f49c493-deef-4804-baea-e6adeb2f56b9',
      apple_id: '',
      google_id: '',
      name: 'Tobias_Brandt12',
      identifier: '50cf2621-7784-428a-a263-14bfe7565099',
      created_at: '2024-10-08T19:25:00.000Z',
      updated_at: '2024-10-08T19:25:00.000Z',
      avatar: 'image_5662bf07e381d861a6cf.png',
      email: null,
      phone_number: null,
      id: '266584',
      avatarUrl: 'https://static.sportiz.app/image_5662bf07e381d861a6cf.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '090d760c-4107-49ef-b28a-5cad4d5bcde6': {
    userName: 'Sebastian_B\u00f6hm52',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '13f10c48-54b6-4643-a709-06981589eef1',
      apple_id: '',
      google_id: '',
      name: 'Sebastian_B\u00f6hm52',
      identifier: '090d760c-4107-49ef-b28a-5cad4d5bcde6',
      created_at: '2024-10-08T19:25:01.000Z',
      updated_at: '2024-10-08T19:25:01.000Z',
      avatar: 'image_04f6d42c1db013bcab37.png',
      email: null,
      phone_number: null,
      id: '266585',
      avatarUrl: 'https://static.sportiz.app/image_04f6d42c1db013bcab37.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '52c12db3-5e12-401c-a9b3-e62793dab180': {
    userName: 'Juliana_Noite18',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'f3ad004c-1044-4605-a1e2-5fe763950432',
      apple_id: '',
      google_id: '',
      name: 'Juliana_Noite18',
      identifier: '52c12db3-5e12-401c-a9b3-e62793dab180',
      created_at: '2024-10-08T19:25:02.000Z',
      updated_at: '2024-10-08T19:25:02.000Z',
      avatar: 'image_4ab24ba85c764c981c4b.png',
      email: null,
      phone_number: null,
      id: '266586',
      avatarUrl: 'https://static.sportiz.app/image_4ab24ba85c764c981c4b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'c54f18d9-ff27-43ce-8c77-6a57ac4763b1': {
    userName: 'Ella_Wizard26',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5418b79d-834c-4333-91ca-17bddac4978f',
      apple_id: '',
      google_id: '',
      name: 'Ella_Wizard26',
      identifier: 'c54f18d9-ff27-43ce-8c77-6a57ac4763b1',
      created_at: '2024-10-08T19:25:05.000Z',
      updated_at: '2024-10-08T19:25:05.000Z',
      avatar: 'image_bea5562cce2202115f8a.png',
      email: null,
      phone_number: null,
      id: '266587',
      avatarUrl: 'https://static.sportiz.app/image_bea5562cce2202115f8a.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '26a1e63a-4c8b-4f3f-a19b-9ae01c55695a': {
    userName: 'Lara_Falc\u00e3o56',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '6ac23ea6-dfcf-4fe1-bae7-bc8c812797ec',
      apple_id: '',
      google_id: '',
      name: 'Lara_Falc\u00e3o56',
      identifier: '26a1e63a-4c8b-4f3f-a19b-9ae01c55695a',
      created_at: '2024-10-08T19:25:05.000Z',
      updated_at: '2024-10-08T19:25:05.000Z',
      avatar: 'image_3fe8adde77c0e12e0672.png',
      email: null,
      phone_number: null,
      id: '266588',
      avatarUrl: 'https://static.sportiz.app/image_3fe8adde77c0e12e0672.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '1242ab01-59c1-49fa-8b64-11dc1ac13497': {
    userName: 'KevinTheKing',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4e823353-e4ab-43e7-8910-7845f2a6fb58',
      apple_id: '',
      google_id: '',
      name: 'KevinTheKing',
      identifier: '1242ab01-59c1-49fa-8b64-11dc1ac13497',
      created_at: '2024-10-08T19:25:08.000Z',
      updated_at: '2024-10-08T19:25:08.000Z',
      avatar: 'image_26c6b480556b12c8231b.png',
      email: null,
      phone_number: null,
      id: '266589',
      avatarUrl: 'https://static.sportiz.app/image_26c6b480556b12c8231b.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '03c56757-e47d-4ca2-aad0-d696c99cd446': {
    userName: 'Daniela_Estrela64',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '2d84cba1-058e-4b64-89fb-5340c76d577f',
      apple_id: '',
      google_id: '',
      name: 'Daniela_Estrela64',
      identifier: '03c56757-e47d-4ca2-aad0-d696c99cd446',
      created_at: '2024-10-08T19:25:09.000Z',
      updated_at: '2024-10-08T19:25:09.000Z',
      avatar: 'image_31d1c45732915a98713e.png',
      email: null,
      phone_number: null,
      id: '266590',
      avatarUrl: 'https://static.sportiz.app/image_31d1c45732915a98713e.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'fb471b8b-76c3-4ba4-b2b6-762917ba0ca0': {
    userName: 'Cody_Storm29',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '0767b0bb-54e1-4e69-91cb-30e8b683ee2b',
      apple_id: '',
      google_id: '',
      name: 'Cody_Storm29',
      identifier: 'fb471b8b-76c3-4ba4-b2b6-762917ba0ca0',
      created_at: '2024-10-08T19:25:11.000Z',
      updated_at: '2024-10-08T19:25:11.000Z',
      avatar: 'image_9b5d517422907c0d5108.png',
      email: null,
      phone_number: null,
      id: '266591',
      avatarUrl: 'https://static.sportiz.app/image_9b5d517422907c0d5108.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '5ada3162-**************-0b53c6aeaf81': {
    userName: 'TiagoMend3s',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '48d7a172-1a73-41aa-97f1-f721bdaa5c40',
      apple_id: '',
      google_id: '',
      name: 'TiagoMend3s',
      identifier: '5ada3162-**************-0b53c6aeaf81',
      created_at: '2024-10-08T19:25:12.000Z',
      updated_at: '2024-10-08T19:25:12.000Z',
      avatar: 'image_ac55b34f7a363cf6710c.png',
      email: null,
      phone_number: null,
      id: '266592',
      avatarUrl: 'https://static.sportiz.app/image_ac55b34f7a363cf6710c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'c8cf0e04-d096-41ce-9c3c-c7fc2595fec4': {
    userName: 'Tom_Dragon41',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'a8a13662-1ed5-4f8a-9fc6-69556740a1e8',
      apple_id: '',
      google_id: '',
      name: 'Tom_Dragon41',
      identifier: 'c8cf0e04-d096-41ce-9c3c-c7fc2595fec4',
      created_at: '2024-10-08T19:25:15.000Z',
      updated_at: '2024-10-08T19:25:15.000Z',
      avatar: 'image_9d60ce065c5210fe32ad.png',
      email: null,
      phone_number: null,
      id: '266594',
      avatarUrl: 'https://static.sportiz.app/image_9d60ce065c5210fe32ad.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '70afab45-cf11-4085-88a2-c97bfed705f1': {
    userName: 'Mariana_Trov\u00e3o37',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5da2999e-60dc-4a04-8624-80205f46d094',
      apple_id: '',
      google_id: '',
      name: 'Mariana_Trov\u00e3o37',
      identifier: '70afab45-cf11-4085-88a2-c97bfed705f1',
      created_at: '2024-10-08T19:25:17.000Z',
      updated_at: '2024-10-08T19:25:17.000Z',
      avatar: 'image_5ae0518bede708605a44.png',
      email: null,
      phone_number: null,
      id: '266595',
      avatarUrl: 'https://static.sportiz.app/image_5ae0518bede708605a44.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '78060c46-c1ff-4869-a615-e7c92acd51c4': {
    userName: 'JohnGammond_21',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'b9d89c2c-3343-461e-9fbd-5c64daf4c6b5',
      apple_id: '',
      google_id: '',
      name: 'JohnGammond_21',
      identifier: '78060c46-c1ff-4869-a615-e7c92acd51c4',
      created_at: '2024-10-08T19:25:18.000Z',
      updated_at: '2024-10-08T19:25:18.000Z',
      avatar: 'image_eaadea8f2857fe3cf040.png',
      email: null,
      phone_number: null,
      id: '266596',
      avatarUrl: 'https://static.sportiz.app/image_eaadea8f2857fe3cf040.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '7d7974bb-41a0-4510-836a-d07bce93ab66': {
    userName: 'AdamStorm',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '6b1ca044-2010-44d5-84ef-fcb160e899a4',
      apple_id: '',
      google_id: '',
      name: 'AdamStorm',
      identifier: '7d7974bb-41a0-4510-836a-d07bce93ab66',
      created_at: '2024-10-08T19:25:21.000Z',
      updated_at: '2024-10-08T19:25:21.000Z',
      avatar: 'image_b8fd32fbeb08e72a2b82.png',
      email: null,
      phone_number: null,
      id: '266597',
      avatarUrl: 'https://static.sportiz.app/image_b8fd32fbeb08e72a2b82.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0fbae443-3e9c-44ef-af12-298dd1c7268a': {
    userName: 'Daniel_Huber95',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4cbe2b30-c3d4-4570-ac9d-04ac78af4063',
      apple_id: '',
      google_id: '',
      name: 'Daniel_Huber95',
      identifier: '0fbae443-3e9c-44ef-af12-298dd1c7268a',
      created_at: '2024-10-08T19:25:22.000Z',
      updated_at: '2024-10-08T19:25:22.000Z',
      avatar: 'image_9040b0a025b295acb5b1.png',
      email: null,
      phone_number: null,
      id: '266598',
      avatarUrl: 'https://static.sportiz.app/image_9040b0a025b295acb5b1.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'dbdd7b52-15c9-4054-99ce-e4f6b980ac55': {
    userName: 'Matthew_Spirit51',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4e170c44-3be4-4ac3-abf5-4fa34a1f5b2d',
      apple_id: '',
      google_id: '',
      name: 'Matthew_Spirit51',
      identifier: 'dbdd7b52-15c9-4054-99ce-e4f6b980ac55',
      created_at: '2024-10-08T19:25:23.000Z',
      updated_at: '2024-10-08T19:25:23.000Z',
      avatar: 'image_ca23f01ea221f58f8df1.png',
      email: null,
      phone_number: null,
      id: '266599',
      avatarUrl: 'https://static.sportiz.app/image_ca23f01ea221f58f8df1.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '24a7c905-f28b-42ce-910e-00da61fda30d': {
    userName: 'Sarah_Wolf64',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '738d33a9-e070-43a4-90e3-895b7b1a2acd',
      apple_id: '',
      google_id: '',
      name: 'Sarah_Wolf64',
      identifier: '24a7c905-f28b-42ce-910e-00da61fda30d',
      created_at: '2024-10-08T19:25:25.000Z',
      updated_at: '2024-10-08T19:25:25.000Z',
      avatar: 'image_190ba22ae9c6551406c9.png',
      email: null,
      phone_number: null,
      id: '266600',
      avatarUrl: 'https://static.sportiz.app/image_190ba22ae9c6551406c9.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '39db8f74-83d7-41f7-8b06-70f7fe0393b3': {
    userName: 'ChrisPan',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '4d10cd1c-6357-44b0-be0e-1d35f5d33666',
      apple_id: '',
      google_id: '',
      name: 'ChrisPan',
      identifier: '39db8f74-83d7-41f7-8b06-70f7fe0393b3',
      created_at: '2024-10-08T19:25:26.000Z',
      updated_at: '2024-10-08T19:25:26.000Z',
      avatar: 'image_5349448f17b63da94d97.png',
      email: null,
      phone_number: null,
      id: '266601',
      avatarUrl: 'https://static.sportiz.app/image_5349448f17b63da94d97.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '73d13eeb-85c1-4882-b1ad-4fb64dcc84f8': {
    userName: 'Klaus_Berger45',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1db3d6ec-c323-49bf-a691-088188603586',
      apple_id: '',
      google_id: '',
      name: 'Klaus_Berger45',
      identifier: '73d13eeb-85c1-4882-b1ad-4fb64dcc84f8',
      created_at: '2024-10-08T19:25:28.000Z',
      updated_at: '2024-10-08T19:25:28.000Z',
      avatar: 'image_831f38c932e7cd31988c.png',
      email: null,
      phone_number: null,
      id: '266602',
      avatarUrl: 'https://static.sportiz.app/image_831f38c932e7cd31988c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '71fde9e1-b7f5-48d8-a41e-f38b832939f8': {
    userName: 'Jessica_Lang18',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'd8d7145f-6700-48a1-9cba-eb53d371274b',
      apple_id: '',
      google_id: '',
      name: 'Jessica_Lang18',
      identifier: '71fde9e1-b7f5-48d8-a41e-f38b832939f8',
      created_at: '2024-10-08T19:25:31.000Z',
      updated_at: '2024-10-08T19:25:31.000Z',
      avatar: 'image_89b45fa247f91eefd5bb.png',
      email: null,
      phone_number: null,
      id: '266603',
      avatarUrl: 'https://static.sportiz.app/image_89b45fa247f91eefd5bb.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '37c28130-440f-45a8-a3c1-c66b70bda60f': {
    userName: 'Henrique_Sombra51',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '52c26d77-1ab5-4e8d-9d63-72eaaa97f5e7',
      apple_id: '',
      google_id: '',
      name: 'Henrique_Sombra51',
      identifier: '37c28130-440f-45a8-a3c1-c66b70bda60f',
      created_at: '2024-10-08T19:25:34.000Z',
      updated_at: '2024-10-08T19:25:34.000Z',
      avatar: 'image_f2f62f19f417259a78ee.png',
      email: null,
      phone_number: null,
      id: '266604',
      avatarUrl: 'https://static.sportiz.app/image_f2f62f19f417259a78ee.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '3ccd1a3f-c8da-4bd8-9c73-ec630f7dce28': {
    userName: 'gab_hunt',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'b8af2d65-eaa5-4834-ac7a-84d9f62fb6cf',
      apple_id: '',
      google_id: '',
      name: 'gab_hunt',
      identifier: '3ccd1a3f-c8da-4bd8-9c73-ec630f7dce28',
      created_at: '2024-10-08T19:25:35.000Z',
      updated_at: '2024-10-08T19:25:35.000Z',
      avatar: 'image_f85b3705642aee091cae.png',
      email: null,
      phone_number: null,
      id: '266605',
      avatarUrl: 'https://static.sportiz.app/image_f85b3705642aee091cae.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '88cdc3b4-9ce6-4318-b9ed-580d4446ece2': {
    userName: 'Isabel_Alma81',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'aef6b625-33cd-4627-85c4-7608f11860c2',
      apple_id: '',
      google_id: '',
      name: 'Isabel_Alma81',
      identifier: '88cdc3b4-9ce6-4318-b9ed-580d4446ece2',
      created_at: '2024-10-08T19:25:36.000Z',
      updated_at: '2024-10-08T19:25:36.000Z',
      avatar: 'image_8217d35dd9eb9f052378.png',
      email: null,
      phone_number: null,
      id: '266606',
      avatarUrl: 'https://static.sportiz.app/image_8217d35dd9eb9f052378.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '2ee76864-6ab3-43b0-badb-8ecc07dcbb06': {
    userName: 'Katharina_Vogel93',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1e1046fe-64cc-4968-8154-86b216315b44',
      apple_id: '',
      google_id: '',
      name: 'Katharina_Vogel93',
      identifier: '2ee76864-6ab3-43b0-badb-8ecc07dcbb06',
      created_at: '2024-10-08T19:25:37.000Z',
      updated_at: '2024-10-08T19:25:37.000Z',
      avatar: 'image_63c7f2e568066f881de7.png',
      email: null,
      phone_number: null,
      id: '266607',
      avatarUrl: 'https://static.sportiz.app/image_63c7f2e568066f881de7.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'fad3e5cc-47dd-474a-838b-52fb373cc046': {
    userName: 'James_Pro87',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '3f5d96df-cd79-41fd-95c9-c0f9891d9826',
      apple_id: '',
      google_id: '',
      name: 'James_Pro87',
      identifier: 'fad3e5cc-47dd-474a-838b-52fb373cc046',
      created_at: '2024-10-08T19:25:39.000Z',
      updated_at: '2024-10-08T19:25:39.000Z',
      avatar: 'image_9fca63b19a0ba764bfcf.png',
      email: null,
      phone_number: null,
      id: '266608',
      avatarUrl: 'https://static.sportiz.app/image_9fca63b19a0ba764bfcf.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0631fe22-a23a-401f-871e-a6b2cb99abd3': {
    userName: 'Vanessa_Koch81',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '9efe7b44-9a54-413e-9a40-83a98f7fc6ea',
      apple_id: '',
      google_id: '',
      name: 'Vanessa_Koch81',
      identifier: '0631fe22-a23a-401f-871e-a6b2cb99abd3',
      created_at: '2024-10-08T19:25:43.000Z',
      updated_at: '2024-10-08T19:25:43.000Z',
      avatar: 'image_f34b62e27eb7467db643.png',
      email: null,
      phone_number: null,
      id: '266609',
      avatarUrl: 'https://static.sportiz.app/image_f34b62e27eb7467db643.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '9f9a1073-69c9-4a56-924f-da8c4f9bd331': {
    userName: 'Gabriela_Fogo42',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '8f2b9041-b12b-446b-936e-584f956521ba',
      apple_id: '',
      google_id: '',
      name: 'Gabriela_Fogo42',
      identifier: '9f9a1073-69c9-4a56-924f-da8c4f9bd331',
      created_at: '2024-10-08T19:25:45.000Z',
      updated_at: '2024-10-08T19:25:45.000Z',
      avatar: 'image_a8891623332f7c38173e.png',
      email: null,
      phone_number: null,
      id: '266610',
      avatarUrl: 'https://static.sportiz.app/image_a8891623332f7c38173e.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '244423ce-2d91-4438-a1ad-ae5966e39015': {
    userName: 'Camila_S\u00e1bia49',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'd9347b24-ca05-4ad1-a161-ca3e9203075e',
      apple_id: '',
      google_id: '',
      name: 'Camila_S\u00e1bia49',
      identifier: '244423ce-2d91-4438-a1ad-ae5966e39015',
      created_at: '2024-10-08T19:25:48.000Z',
      updated_at: '2024-10-08T19:25:48.000Z',
      avatar: 'image_72370eb3bf26093721a2.png',
      email: null,
      phone_number: null,
      id: '266611',
      avatarUrl: 'https://static.sportiz.app/image_72370eb3bf26093721a2.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '7d43e1f8-ca32-411c-ab17-fa580f0d2db1': {
    userName: 'Lars_Hoffmann99',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'c24beda5-ca94-4687-a9b4-16cb3a7d8b54',
      apple_id: '',
      google_id: '',
      name: 'Lars_Hoffmann99',
      identifier: '7d43e1f8-ca32-411c-ab17-fa580f0d2db1',
      created_at: '2024-10-08T19:25:50.000Z',
      updated_at: '2024-10-08T19:25:50.000Z',
      avatar: 'image_8bc8d63dc9b11935c92c.png',
      email: null,
      phone_number: null,
      id: '266612',
      avatarUrl: 'https://static.sportiz.app/image_8bc8d63dc9b11935c92c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'e790d43a-1ea0-4699-9875-814d9e54de4e': {
    userName: 'terrencel',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '1fc24092-9c32-46fb-b82c-aa77a6c46c01',
      apple_id: '',
      google_id: '',
      name: 'terrencel',
      identifier: 'e790d43a-1ea0-4699-9875-814d9e54de4e',
      created_at: '2024-10-08T19:25:52.000Z',
      updated_at: '2024-10-08T19:25:52.000Z',
      avatar: 'image_44c1b50cd8e2159d373c.png',
      email: null,
      phone_number: null,
      id: '266613',
      avatarUrl: 'https://static.sportiz.app/image_44c1b50cd8e2159d373c.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '9e5c42e5-3127-42ab-8860-a0b9f44b3706': {
    userName: 'Simon_Baum58',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '07e2eec8-0132-41e3-8b08-680abfac7aed',
      apple_id: '',
      google_id: '',
      name: 'Simon_Baum58',
      identifier: '9e5c42e5-3127-42ab-8860-a0b9f44b3706',
      created_at: '2024-10-08T19:25:54.000Z',
      updated_at: '2024-10-08T19:25:54.000Z',
      avatar: 'image_3d556c26bd2d2760bb6f.png',
      email: null,
      phone_number: null,
      id: '266614',
      avatarUrl: 'https://static.sportiz.app/image_3d556c26bd2d2760bb6f.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0a88cf63-edb7-410b-9e41-f88987cba6d7': {
    userName: 'Gina_Blaze42',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '52b8984a-c0bf-4c9d-bd5a-890537a78cd8',
      apple_id: '',
      google_id: '',
      name: 'Gina_Blaze42',
      identifier: '0a88cf63-edb7-410b-9e41-f88987cba6d7',
      created_at: '2024-10-08T19:25:56.000Z',
      updated_at: '2024-10-08T19:25:56.000Z',
      avatar: 'image_a386c3ad919c256223dd.png',
      email: null,
      phone_number: null,
      id: '266615',
      avatarUrl: 'https://static.sportiz.app/image_a386c3ad919c256223dd.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '44c22549-ef6b-427e-b4f4-81967435e993': {
    userName: 'Matt_Falcon',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'ed5fa72f-30e7-40a5-9f3b-1be0cc074d59',
      apple_id: '',
      google_id: '',
      name: 'Matt_Falcon',
      identifier: '44c22549-ef6b-427e-b4f4-81967435e993',
      created_at: '2024-10-08T19:26:00.000Z',
      updated_at: '2024-10-08T19:26:00.000Z',
      avatar: 'image_8080d3b361e923a6b7b2.png',
      email: null,
      phone_number: null,
      id: '266616',
      avatarUrl: 'https://static.sportiz.app/image_8080d3b361e923a6b7b2.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '2826f5d2-5d2b-4455-85c4-4e8db15334be': {
    userName: 'ronsun',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '6065e50e-a368-4ae4-aa8b-dd7c24f44585',
      apple_id: '',
      google_id: '',
      name: 'ronsun',
      identifier: '2826f5d2-5d2b-4455-85c4-4e8db15334be',
      created_at: '2024-10-08T19:26:02.000Z',
      updated_at: '2024-10-08T19:26:02.000Z',
      avatar: 'image_a936cb37dee755e50acf.png',
      email: null,
      phone_number: null,
      id: '266618',
      avatarUrl: 'https://static.sportiz.app/image_a936cb37dee755e50acf.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '7ad799c6-f189-4111-900c-906a0c0968a6': {
    userName: 'Sandra_Schulz59',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '5f94bb75-63ca-4cba-b369-e6f1ded16867',
      apple_id: '',
      google_id: '',
      name: 'Sandra_Schulz59',
      identifier: '7ad799c6-f189-4111-900c-906a0c0968a6',
      created_at: '2024-10-08T19:26:05.000Z',
      updated_at: '2024-10-08T19:26:05.000Z',
      avatar: 'image_c86d514e5f012000adc4.png',
      email: null,
      phone_number: null,
      id: '266619',
      avatarUrl: 'https://static.sportiz.app/image_c86d514e5f012000adc4.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  '0b4a1032-188a-4651-afab-9863db3c14c5': {
    userName: 'Stefan_Kaiser76',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: 'b46765af-2f29-4b50-8d7c-fe4a84a7f8b9',
      apple_id: '',
      google_id: '',
      name: 'Stefan_Kaiser76',
      identifier: '0b4a1032-188a-4651-afab-9863db3c14c5',
      created_at: '2024-10-08T19:26:07.000Z',
      updated_at: '2024-10-08T19:26:07.000Z',
      avatar: 'image_3b69ab9b5a3c908ab61a.png',
      email: null,
      phone_number: null,
      id: '266620',
      avatarUrl: 'https://static.sportiz.app/image_3b69ab9b5a3c908ab61a.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
  'b318ef64-0da1-4aba-b1c5-87b112e15f1e': {
    userName: 'ScottGarrett',
    isConnected: true,
    isBot: true,
    connectionId: '',
    id: 0,
    userId: 1,
    quizId: 1,
    user: {
      uuid: '90fb1710-8a85-4e50-a89e-394c11b274ff',
      apple_id: '',
      google_id: '',
      name: 'ScottGarrett',
      identifier: 'b318ef64-0da1-4aba-b1c5-87b112e15f1e',
      created_at: '2024-10-08T19:26:08.000Z',
      updated_at: '2024-10-08T19:26:08.000Z',
      avatar: 'image_b423f15e14a1d3b9c0f7.png',
      email: null,
      phone_number: null,
      id: '266621',
      avatarUrl: 'https://static.sportiz.app/image_b423f15e14a1d3b9c0f7.png',
    },
    roomOwner: false,
    roomName: '',
    socketId: '',
    updated_at: '',
  },
};

(async () => {
  console.log('Iniciando processamento de múltiplos usuários...');
  const resultadoFinal = await processarMultiplosUsuarios(
    dadosDeEntradaMultiplosExemplo,
  );
  console.log('\n--- FIM DO PROCESSAMENTO ---');
  console.log('Resultado final:', resultadoFinal);
})();
